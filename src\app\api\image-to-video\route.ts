import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';
import sharp from 'sharp';

// Create a Supabase client specifically for server-side use with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

// Configure the FAL client with the API key
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Video cost mapping (in tokens) - based on fal.ai pricing
const VIDEO_COSTS = {
  '1080p': 62 // $0.62 = 62 tokens for 1080p 5-second video
};

interface ImageToVideoRequest {
  prompt: string;
  duration?: number;
  resolution?: string;
}

// Utility function to process image for FAL
async function processImageForFAL(imageFile: File): Promise<{ buffer: Buffer; contentType: string; fileName: string }> {
  const MAX_DIMENSION = 3840;
  const QUALITY = 85;

  try {
    const arrayBuffer = await imageFile.arrayBuffer();
    const inputBuffer = Buffer.from(arrayBuffer);
    
    const metadata = await sharp(inputBuffer).metadata();
    const { width = 0, height = 0, format } = metadata;
    
    console.log(`Original image: ${width}x${height}, format: ${format}`);
    
    const needsResize = width > MAX_DIMENSION || height > MAX_DIMENSION;
    
    if (!needsResize) {
      let processedBuffer = inputBuffer;
      let outputFormat = format;
      
      if (format && !['jpeg', 'jpg', 'png'].includes(format.toLowerCase())) {
        processedBuffer = await sharp(inputBuffer)
          .rotate()
          .jpeg({ quality: QUALITY })
          .toBuffer();
        outputFormat = 'jpeg';
        console.log(`Converted ${format} to JPEG for compatibility`);
      } else {
        const needsRotation = metadata.orientation && metadata.orientation !== 1;
        if (needsRotation) {
          processedBuffer = await sharp(inputBuffer)
            .rotate()
            .toBuffer();
          console.log(`Applied EXIF orientation correction`);
        }
      }
      
      return {
        buffer: processedBuffer,
        contentType: outputFormat === 'png' ? 'image/png' : 'image/jpeg',
        fileName: imageFile.name.replace(/\.[^/.]+$/, '') + (outputFormat === 'png' ? '.png' : '.jpg')
      };
    }
    
    const maxCurrentDimension = Math.max(width, height);
    const scaleFactor = Math.min(MAX_DIMENSION / maxCurrentDimension, 1.0);
    
    const newWidth = Math.round(width * scaleFactor);
    const newHeight = Math.round(height * scaleFactor);
    
    console.log(`Resizing image from ${width}x${height} to ${newWidth}x${newHeight}`);
    
    const resizedBuffer = await sharp(inputBuffer)
      .rotate()
      .resize(newWidth, newHeight, {
        fit: 'inside',
        kernel: sharp.kernel.lanczos3,
        withoutEnlargement: true
      })
      .jpeg({ 
        quality: QUALITY,
        progressive: true
      })
      .toBuffer();
    
    const originalSizeMB = (inputBuffer.length / 1024 / 1024).toFixed(2);
    const newSizeMB = (resizedBuffer.length / 1024 / 1024).toFixed(2);
    console.log(`Image size reduced from ${originalSizeMB}MB to ${newSizeMB}MB`);
    
    return {
      buffer: resizedBuffer,
      contentType: 'image/jpeg',
      fileName: imageFile.name.replace(/\.[^/.]+$/, '') + '_resized.jpg'
    };
    
  } catch (error) {
    console.error('Image processing error:', error);
    const arrayBuffer = await imageFile.arrayBuffer();
    return {
      buffer: Buffer.from(arrayBuffer),
      contentType: imageFile.type,
      fileName: imageFile.name
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      return NextResponse.json(
        { error: 'Supabase configuration missing' },
        { status: 500 }
      );
    }

    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    const prompt = formData.get('prompt') as string;
    const duration = parseInt(formData.get('duration') as string) || 5;
    const resolution = formData.get('resolution') as string || '1080p';
    
    console.log('Received formData:', { hasImage: !!imageFile, prompt, duration, resolution });
    if (!imageFile || !prompt) {
      return NextResponse.json(
        { error: 'Image file and prompt are required' },
        { status: 400 }
      );
    }

    // Get user from auth header or cookie
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    console.log('Auth header:', authHeader);
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required - no token found' },
        { status: 401 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    console.log('User fetch:', { user, authError });
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        { error: 'Invalid authentication', details: authError?.message },
        { status: 401 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();
    console.log('Wallet fetch:', { wallet, walletError });
    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Calculate cost
    const videoCost = VIDEO_COSTS[resolution as keyof typeof VIDEO_COSTS] || VIDEO_COSTS['1080p'];
    console.log('Video cost:', videoCost, 'Wallet balance:', wallet.balance);
    // Check if user has sufficient balance
    if (wallet.balance < videoCost) {
      return NextResponse.json(
        { error: 'Insufficient token balance', required: videoCost, available: wallet.balance },
        { status: 402 }
      );
    }

    // Process and upload image
    const { buffer, contentType, fileName: processedFileName } = await processImageForFAL(imageFile);
    console.log('Processed image:', { processedFileName, contentType, bufferLength: buffer.length });
    const uploadFileName = `image-to-video/${user.id}/${Date.now()}-${processedFileName.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('generated-images')
      .upload(uploadFileName, buffer, {
        contentType: contentType,
        upsert: false
      });
    console.log('Image upload:', { uploadData, uploadError });
    if (uploadError) {
      console.error('Upload error:', uploadError);
      return NextResponse.json(
        { error: 'Failed to upload image', details: uploadError.message },
        { status: 500 }
      );
    }

    // Get public URL for the uploaded image
    const { data: { publicUrl: imageUrl } } = supabaseAdmin.storage
      .from('generated-images')
      .getPublicUrl(uploadFileName);
    console.log('Image public URL:', imageUrl);
    // Create video generation request record
    const { data: requestData, error: requestError } = await supabaseAdmin
      .from('image_to_video_requests')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        status: 'processing',
        image_url: imageUrl,
        prompt: prompt,
        duration: duration,
        resolution: resolution,
        estimated_cost: videoCost / 100,
        token_cost: videoCost
      })
      .select()
      .single();
    console.log('Request insert:', { requestData, requestError });
    if (requestError) {
      console.error('Request creation error:', requestError);
      return NextResponse.json(
        { error: 'Failed to create video generation request' },
        { status: 500 }
      );
    }

    // Deduct tokens from wallet
    await supabaseAdmin
      .from('wallets')
      .update({ 
        balance: wallet.balance - videoCost,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet.id);

    // Create token transaction record
    await supabaseAdmin
      .from('token_transactions')
      .insert({
        wallet_id: wallet.id,
        amount: -videoCost,
        description: `Image to video generation - ${resolution}`,
        transaction_type: 'consumption'
      });

    // Call FAL API for video generation using Seedance model
    try {
      const result = await fal.subscribe("fal-ai/bytedance/seedance/v1/pro/image-to-video", {
        input: {
          prompt: prompt,
          image_url: imageUrl,
          duration: duration,
          resolution: resolution
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            console.log("Processing video:", update.logs?.map((log) => log.message).join('\n'));
          }
        },
      });

      if (!result.data || !result.data.video) {
        throw new Error('No video returned from API');
      }

      const videoUrl = result.data.video.url;

      // Download and upload video to Supabase storage
      const videoResponse = await fetch(videoUrl);
      const videoBuffer = await videoResponse.arrayBuffer();
      
      const videoFileName = `image-to-video/${user.id}/${Date.now()}-generated-video.mp4`;
      
      const { data: videoUploadData, error: videoUploadError } = await supabaseAdmin.storage
        .from('generated-videos')
        .upload(videoFileName, videoBuffer, {
          contentType: 'video/mp4',
          upsert: false
        });

      if (videoUploadError) {
        console.error('Video upload error:', videoUploadError);
        // Still continue with the original URL if upload fails
      }

      // Get public URL for the uploaded video (or use original if upload failed)
      const { data: { publicUrl: finalVideoUrl } } = supabaseAdmin.storage
        .from('generated-videos')
        .getPublicUrl(videoFileName);

      const storedVideoUrl = videoUploadError ? videoUrl : finalVideoUrl;

      // Update request with success
      await supabaseAdmin
        .from('image_to_video_requests')
        .update({
          status: 'completed',
          video_url: storedVideoUrl,
          fal_request_id: result.requestId,
          actual_cost: videoCost / 100,
          completed_at: new Date().toISOString()
        })
        .eq('id', requestData.id);

      // Create video record
      await supabaseAdmin
        .from('generated_videos')
        .insert({
          user_id: user.id,
          wallet_id: wallet.id,
          request_id: requestData.id,
          video_url: storedVideoUrl,
          duration: duration,
          resolution: resolution,
          prompt_text: prompt,
          source_image_url: imageUrl,
          fal_request_id: result.requestId,
          fal_model: 'fal-ai/bytedance/seedance/v1/pro/image-to-video',
          token_cost: videoCost
        });

      return NextResponse.json({
        request_id: requestData.id,
        status: 'completed',
        video: {
          url: storedVideoUrl,
          duration: duration,
          resolution: resolution,
          prompt: prompt
        },
        image_url: imageUrl,
        cost: videoCost / 100,
        tokens_charged: videoCost,
        remaining_balance: wallet.balance - videoCost
      });

    } catch (falError: any) {
      console.error('FAL video generation error:', falError);

      // Update request with failure
      await supabaseAdmin
        .from('image_to_video_requests')
        .update({
          status: 'failed',
          error_message: falError.message || falError.toString(),
          completed_at: new Date().toISOString()
        })
        .eq('id', requestData.id);

      // Refund the tokens
      await supabaseAdmin
        .from('wallets')
        .update({ 
          balance: wallet.balance,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id);

      // Create refund transaction
      await supabaseAdmin
        .from('token_transactions')
        .insert({
          wallet_id: wallet.id,
          amount: videoCost,
          description: `Refund: Image to video generation failed - ${falError.message || 'Unknown error'}`,
          transaction_type: 'refund'
        });

      return NextResponse.json(
        { 
          error: 'Video generation failed', 
          details: falError.message || falError.toString(),
          request_id: requestData.id,
          refunded_tokens: videoCost
        },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error('Image to video error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate video', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
}