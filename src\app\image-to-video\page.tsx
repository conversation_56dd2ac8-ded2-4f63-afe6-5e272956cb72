'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import styles from './page.module.css';
import { supabase } from '../../lib/supabaseClient';
import Header from '@/components/Header';
import { useTranslation } from '@/hooks/useTranslation';
import { Spinner } from 'react-bootstrap';

interface VideoGeneration {
  request_id: string;
  status: string;
  video?: {
    url: string;
    duration: number;
    resolution: string;
    prompt: string;
  };
  image_url: string;
  cost: number;
  tokens_charged: number;
  remaining_balance: number;
}

export default function ImageToVideoPage() {
  const { user, loading } = useSelector((state: RootState) => state.user);
  const router = useRouter();
  const [walletBalance, setWalletBalance] = useState<number | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');
  const [duration, setDuration] = useState(5);
  const [resolution, setResolution] = useState('1080p');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedVideo, setGeneratedVideo] = useState<VideoGeneration | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [userTokens, setUserTokens] = useState<number>(0);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Romanian translations
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  // Fetch wallet balance for the logged-in user
  useEffect(() => {
    async function fetchBalance() {
      if (user && user.id) {
        const { data: wallet, error } = await supabase
          .from('wallets')
          .select('balance')
          .eq('user_id', user.id)
          .maybeSingle();
        if (!error && wallet) {
          setWalletBalance(wallet.balance);
          setUserTokens(wallet.balance);
        }
      }
    }
    fetchBalance();
  }, [user]);

  // Cost per resolution (in tokens)
  const COSTS = {
    '1080p': 62
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setError(null);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedFile(file);
      setError(null);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const generateVideo = async () => {
    if (!selectedFile || !prompt.trim()) {
      setError('Please select an image and enter a prompt');
      return;
    }

    if (!user) {
      setError('Please sign in to generate videos');
      return;
    }

    const cost = COSTS[resolution as keyof typeof COSTS];
    if (userTokens < cost) {
      setError(`Insufficient tokens. You need ${cost} tokens but only have ${userTokens}`);
      return;
    }

    setIsGenerating(true);
    setError(null);
    setGeneratedVideo(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('prompt', prompt);
      formData.append('duration', duration.toString());
      formData.append('resolution', resolution);

      const response = await fetch('/api/image-to-video', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate video');
      }

      const result = await response.json();
      setGeneratedVideo(result);
      setUserTokens(result.remaining_balance);
      
    } catch (error) {
      console.error('Video generation error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsGenerating(false);
    }
  };

  const estimatedCost = COSTS[resolution as keyof typeof COSTS];

  // Loading state (user not yet loaded)
  if (loading) {
    return (
      <>
        <Header />
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginTop: 64 }}>
          <Spinner animation="border" role="status" style={{ width: 60, height: 60, marginBottom: 16 }} />
          <div>{t('image_to_video_loading')}</div>
        </div>
      </>
    );
  }

  // Not logged in
  if (!loading && user === null) {
    return (
      <>
        <Header />
        <div className={styles.authRequired}>
          <p>{t('image_to_video_auth_required')}</p>
          <button onClick={() => router.push('/login')}>{t('image_to_video_auth_button')}</button>
        </div>
      </>
    );
  }

  return (
    <>
      <Header />
    <div className={styles.container}>
      <div className={styles.header}>
          <h1>{t('image_to_video_title')}</h1>
          <p>{t('image_to_video_subtitle')}</p>
          <div style={{ fontSize: 18, fontWeight: 500, margin: '16px 0' }}>
            {t('image_to_video_credits', { balance: walletBalance !== null ? walletBalance : '...' })}
          </div>
        </div>

      {user && (
        <div className={styles.content}>
          <div className={styles.inputSection}>
            <div className={styles.uploadArea}>
                <h3>{t('image_to_video_upload_image')}</h3>
              <div
                className={`${styles.dropZone} ${selectedFile ? styles.hasFile : ''}`}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                {imagePreview ? (
                  <div className={styles.imagePreview}>
                    <img src={imagePreview} alt="Selected" />
                      <p>{t('image_to_video_click_to_change')}</p>
                  </div>
                ) : (
                  <div className={styles.uploadPrompt}>
                      <p>{t('image_to_video_drag_drop')}</p>
                      <p className={styles.fileTypes}>{t('image_to_video_file_types')}</p>
                  </div>
                )}
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
            </div>

            <div className={styles.promptSection}>
                <h3>{t('image_to_video_prompt_label')}</h3>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                  placeholder={t('image_to_video_prompt_placeholder')}
                className={styles.promptInput}
                rows={4}
              />
              <p className={styles.promptHelp}>
                Example: "A person walking through a magical forest with glowing particles around them"
              </p>
            </div>

            <div className={styles.settingsSection}>
              <h3>Video Settings</h3>
              <div className={styles.settingsRow}>
                <div className={styles.setting}>
                  <label>Duration</label>
                  <select
                    value={duration}
                    onChange={(e) => setDuration(parseInt(e.target.value))}
                  >
                    <option value={5}>5 seconds</option>
                    <option value={8}>8 seconds</option>
                    <option value={10}>10 seconds</option>
                  </select>
                </div>
                <div className={styles.setting}>
                  <label>Resolution</label>
                  <select
                    value={resolution}
                    onChange={(e) => setResolution(e.target.value)}
                  >
                    <option value="1080p">1080p (HD)</option>
                  </select>
                </div>
              </div>
              <div className={styles.costInfo}>
                  <p>{t('image_to_video_cost', { cost: estimatedCost })}</p>
              </div>
            </div>

            {error && (
              <div className={styles.error}>
                  {error === 'Please select an image and enter a prompt' && t('image_to_video_error_select_image_and_prompt')}
                  {error === 'Please sign in to generate videos' && t('image_to_video_error_sign_in')}
                  {error.startsWith('Insufficient tokens') && t('image_to_video_insufficient_tokens', { cost: estimatedCost, tokens: userTokens })}
                  {![
                    'Please select an image and enter a prompt',
                    'Please sign in to generate videos',
                  ].includes(error) && !error.startsWith('Insufficient tokens') && error}
              </div>
            )}

            <button
              onClick={generateVideo}
              disabled={isGenerating || !selectedFile || !prompt.trim()}
              className={styles.generateButton}
            >
                {isGenerating ? t('image_to_video_generating') : t('image_to_video_generate')}
            </button>
          </div>

          {generatedVideo && (
            <div className={styles.resultSection}>
              <h3>Generated Video</h3>
              <div className={styles.videoResult}>
                <video
                  src={generatedVideo.video?.url}
                  controls
                  className={styles.generatedVideo}
                  poster={generatedVideo.image_url}
                >
                  Your browser does not support the video tag.
                </video>
                <div className={styles.videoInfo}>
                  <p><strong>Prompt:</strong> {generatedVideo.video?.prompt}</p>
                  <p><strong>Duration:</strong> {generatedVideo.video?.duration}s</p>
                  <p><strong>Resolution:</strong> {generatedVideo.video?.resolution}</p>
                  <p><strong>Cost:</strong> {generatedVideo.tokens_charged} tokens</p>
                  <p><strong>Remaining Balance:</strong> {generatedVideo.remaining_balance} tokens</p>
                </div>
                <div className={styles.downloadSection}>
                  <a
                    href={generatedVideo.video?.url}
                    download="generated-video.mp4"
                    className={styles.downloadButton}
                  >
                    Download Video
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
    </>
  );
}