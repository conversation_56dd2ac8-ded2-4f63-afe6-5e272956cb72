'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import styles from '../../admin/admin.module.css';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface ImageToVideoRequest {
  id: string;
  user_id: string;
  status: string;
  image_url: string;
  video_url?: string;
  prompt: string;
  duration: number;
  resolution: string;
  token_cost: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
  profiles?: {
    email?: string;
    full_name?: string;
  };
}

interface ApiResponse {
  requests: ImageToVideoRequest[];
  stats: {
    total: number;
    completed: number;
    processing: number;
    failed: number;
    pending: number;
  };
}

export default function ImageToVideoGenerationsPage() {
  const [requests, setRequests] = useState<ImageToVideoRequest[]>([]);
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    processing: 0,
    failed: 0,
    pending: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'created_at' | 'status'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedVideo, setSelectedVideo] = useState<ImageToVideoRequest | null>(null);
  const [showVideoModal, setShowVideoModal] = useState(false);

  useEffect(() => {
    fetchRequests();
  }, [filter, sortBy, sortOrder]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchRequests = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('No session found');
      }

      // Build query parameters
      const params = new URLSearchParams({
        filter,
        sortBy,
        sortOrder
      });

      const response = await fetch(`/api/admin/image-to-video-generations?${params}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch requests');
      }

      const data: ApiResponse = await response.json();
      setRequests(data.requests);
      setStats(data.stats);
    } catch (err: any) {
      console.error('Error fetching requests:', err);
      setError(err.message || 'Failed to load image-to-video requests');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10b981';
      case 'processing':
        return '#f59e0b';
      case 'failed':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'processing':
        return '⏳';
      case 'failed':
        return '❌';
      default:
        return '⏸️';
    }
  };

  // Use stats from API response instead of calculating locally

  if (loading) {
    return (
      <div className={styles.container}>
        <h1>Image to Video Generations</h1>
        <div className={styles.loading}>Loading...</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Image to Video Generations</h1>
        <p>Manage and monitor image-to-video generation requests</p>
      </div>

      <div className={styles.stats}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{stats.total}</div>
          <div className={styles.statLabel}>Total Requests</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{stats.completed}</div>
          <div className={styles.statLabel}>Completed</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{stats.processing}</div>
          <div className={styles.statLabel}>Processing</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{stats.failed}</div>
          <div className={styles.statLabel}>Failed</div>
        </div>
      </div>

      <div className={styles.filters}>
        <div className={styles.filterGroup}>
          <label>Filter by Status:</label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label>Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'created_at' | 'status')}
          >
            <option value="created_at">Date Created</option>
            <option value="status">Status</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label>Order:</label>
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </select>
        </div>

        <button onClick={fetchRequests} className={styles.refreshButton}>
          Refresh
        </button>
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      <div className={styles.requestsTable}>
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>User</th>
              <th>Status</th>
              <th>Image</th>
              <th>Prompt</th>
              <th>Settings</th>
              <th>Cost</th>
              <th>Created</th>
              <th>Completed</th>
            </tr>
          </thead>
          <tbody>
            {requests.map((request) => (
              <tr key={request.id}>
                <td>
                  <code>{request.id.slice(0, 8)}</code>
                </td>
                <td>
                  <div className={styles.userInfo}>
                    <div>{request.profiles?.full_name || request.profiles?.email || 'Unknown'}</div>
                    <small>{request.user_id.slice(0, 8)}</small>
                  </div>
                </td>
                <td>
                  <span
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(request.status) }}
                  >
                    {getStatusIcon(request.status)} {request.status}
                  </span>
                </td>
                <td>
                  <div className={styles.imagePreview}>
                    <img
                      src={request.image_url}
                      alt="Source"
                      className={styles.thumbnailImage}
                    />
                    {request.video_url && (
                      <button
                        className={styles.videoIcon}
                        onClick={() => {
                          setSelectedVideo(request);
                          setShowVideoModal(true);
                        }}
                        title="View generated video"
                      >
                        ▶️
                      </button>
                    )}
                  </div>
                </td>
                <td>
                  <div className={styles.promptText}>
                    {request.prompt.substring(0, 100)}
                    {request.prompt.length > 100 && '...'}
                  </div>
                </td>
                <td>
                  <div className={styles.settings}>
                    <div>{request.duration}s</div>
                    <div>{request.resolution}</div>
                  </div>
                </td>
                <td>
                  <div className={styles.cost}>
                    {request.token_cost} tokens
                  </div>
                </td>
                <td>
                  <div className={styles.date}>
                    {formatDate(request.created_at)}
                  </div>
                </td>
                <td>
                  <div className={styles.date}>
                    {request.completed_at ? formatDate(request.completed_at) : '-'}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {requests.length === 0 && (
          <div className={styles.noResults}>
            No image-to-video requests found
          </div>
        )}
      </div>

            {/* Video Modal */}
      {showVideoModal && selectedVideo && selectedVideo.video_url && (
        <div className={styles.compactModalOverlay} onClick={() => setShowVideoModal(false)}>
          <div className={styles.compactModalContent} onClick={(e) => e.stopPropagation()}>
            <button 
              className={styles.compactModalCloseButton}
              onClick={() => setShowVideoModal(false)}
              title="Close"
            >
              ×
            </button>
            <video
              src={selectedVideo.video_url}
              className={styles.compactModalVideo}
              controls
              autoPlay
            />
            <div className={styles.videoInfo}>
              <p><strong>Prompt:</strong> {selectedVideo.prompt.substring(0, 100)}{selectedVideo.prompt.length > 100 ? '...' : ''}</p>
              <p><strong>Duration:</strong> {selectedVideo.duration}s | <strong>Resolution:</strong> {selectedVideo.resolution}</p>
              <p><strong>Cost:</strong> {selectedVideo.token_cost} tokens</p>
              <p><strong>Status:</strong> <span style={{ color: getStatusColor(selectedVideo.status) }}>{selectedVideo.status}</span></p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
