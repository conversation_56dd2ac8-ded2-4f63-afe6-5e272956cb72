.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.header p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1rem;
}

.tokenBalance {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  display: inline-block;
  color: #0369a1;
  font-weight: 600;
}

.authRequired {
  text-align: center;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem 0;
}

.authRequired button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
}

.authRequired button:hover {
  background: #2563eb;
}

.content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.inputSection {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.uploadArea {
  margin-bottom: 2rem;
}

.uploadArea h3 {
  margin-bottom: 1rem;
  color: #374151;
}

.dropZone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f9fafb;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.dropZone:hover {
  border-color: #3b82f6;
  background: #f0f9ff;
}

.dropZone.hasFile {
  border-color: #10b981;
  background: #f0fdf4;
}

.imagePreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.imagePreview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.uploadPrompt p {
  margin: 0.5rem 0;
  color: #6b7280;
}

.fileTypes {
  font-size: 0.875rem;
  color: #9ca3af;
}

.promptSection {
  margin-bottom: 2rem;
}

.promptSection h3 {
  margin-bottom: 1rem;
  color: #374151;
}

.promptInput {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.promptInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.promptHelp {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.settingsSection {
  margin-bottom: 2rem;
}

.settingsSection h3 {
  margin-bottom: 1rem;
  color: #374151;
}

.settingsRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.setting {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.setting select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
}

.setting select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.costInfo {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.costInfo p {
  margin: 0;
  color: #0369a1;
}

.error {
  background: #fef2f2;
  border: 1px solid #f87171;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #dc2626;
}

.generateButton {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.generateButton:hover:not(:disabled) {
  background: #2563eb;
}

.generateButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.resultSection {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-top: 2rem;
}

.resultSection h3 {
  margin-bottom: 1.5rem;
  color: #374151;
}

.videoResult {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.generatedVideo {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.videoInfo {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.videoInfo p {
  margin: 0.5rem 0;
  color: #374151;
}

.downloadSection {
  text-align: center;
}

.downloadButton {
  display: inline-block;
  background: #10b981;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.downloadButton:hover {
  background: #059669;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .settingsRow {
    grid-template-columns: 1fr;
  }
  
  .dropZone {
    min-height: 150px;
    padding: 1rem;
  }
  
  .imagePreview img {
    max-height: 200px;
  }
}