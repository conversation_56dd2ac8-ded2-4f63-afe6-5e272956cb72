.container {
  padding: 2rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.table th,
.table td {
  padding: 0.75rem;
  border: 1px solid #ddd;
  text-align: left;
}

.table th {
  background: #f7f7f7;
  font-weight: bold;
}

.clickableRow {
  cursor: pointer;
}

.clickableRow:hover {
  background-color: #f2f2f2;
}

/* Tab styles */
.tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 2rem;
  margin-top: 1.5rem;
}

.tabButton {
  padding: 1rem 2rem;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  color: #777;
  transition: color 0.3s;
}

.tabButton:hover {
  color: #333;
}

.activeTab {
  color: #0070f3;
  font-weight: 600;
}

.activeTab::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0070f3;
}

.tabContent {
  min-height: 300px;
}

.conversationsSection,
.generationsSection {
  padding: 1rem 0;
}

/* Section headers */
.conversationsSection h2,
.generationsSection h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

/* Conversation stats styles */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.statCard {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.statCard h3 {
  font-size: 1rem;
  color: #555;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.statValue {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0070f3;
  margin-bottom: 0.5rem;
}

.statDetails {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  font-size: 0.875rem;
  color: #777;
}

.statDetails span {
  margin-bottom: 0.25rem;
}

.recentActivity {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.recentActivity h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
}

.recentUsers {
  margin-top: 1rem;
}

.usersList {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.userItem {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.userItem:hover {
  transform: translateY(-2px);
}

.userIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #4a6cf7;
  color: white;
  font-weight: bold;
  margin-right: 1rem;
}

.userDetails {
  display: flex;
  flex-direction: column;
}

.userName {
  font-weight: 600;
  color: #333;
}

.userId {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

.userActivity {
  font-size: 0.8rem;
  color: #888;
  margin-top: 0.25rem;
}

/* Daily stats chart */
.dailyStats {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 2.5rem;
}

.dailyStats h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
}

.barChart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200px;
  margin-top: 1.5rem;
  padding-bottom: 2rem;
  position: relative;
}

.barChart::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 2rem;
  height: 1px;
  background-color: #eee;
}

.barContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 0.5rem;
  position: relative;
  height: 100%;
}

.bar {
  width: 40px;
  max-width: 100%;
  background-color: #0070f3;
  border-radius: 4px 4px 0 0;
  position: absolute;
  bottom: 0;
  max-height: 160px; /* This ensures bars stay within the chart area */
}

.barLabel {
  position: absolute;
  bottom: -2rem;
  font-size: 0.75rem;
  color: #777;
}

.barCount {
  position: absolute;
  bottom: -3.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
}

.botActivity {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.botActivity h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
}

/* Common styles for both tabs */
.loadingText, 
.errorText {
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.errorText {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 1rem;
  border-radius: 4px;
}

.statsText {
  margin-bottom: 1.5rem;
  font-weight: 500;
  color: #555;
}

/* Generation tab styles */
.imagesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.imageCard {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background-color: #fff;
  transition: transform 0.2s, box-shadow 0.2s;
}

.imageCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.cardImageContainer {
  width: 100%;
  height: 220px;
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.generatedImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.imageDetails {
  padding: 1.25rem;
}

.imageDate {
  font-size: 0.875rem;
  color: #777;
  margin-bottom: 0.75rem;
}

.imagePrompt {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #333;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.imageSource {
  display: inline-block;
  font-size: 0.8125rem;
  color: #fff;
  background-color: #0070f3;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  margin-bottom: 0.75rem;
}

.userInfo {
  font-size: 0.875rem;
  color: #555;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #eee;
}

/* Active Bots Section */
.activeBots {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 2.5rem;
}

.activeBots h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1.25rem;
}

.botsList {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.botCard {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  width: calc(33.333% - 1rem);
  min-width: 250px;
  border-left: 4px solid #0070f3;
  transition: transform 0.2s, box-shadow 0.2s;
}

.botCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.botIcon {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.telegramIcon, .whatsappIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
  color: white;
}

.telegramIcon {
  background-color: #0088cc;
}

.whatsappIcon {
  background-color: #25D366;
}

.botInfo {
  flex: 1;
}

.botInfo h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: #333;
}

.botProvider {
  font-size: 0.875rem;
  color: #666;
  margin: 0 0 0.25rem 0;
  text-transform: capitalize;
}

.botId {
  font-size: 0.75rem;
  color: #888;
  margin: 0;
  font-family: monospace;
}

/* Provider badges for bot activity table */
.botName {
  font-weight: 500;
}

.botProviderCell {
  padding: 0.5rem 0.75rem;
}

.providerBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}

.telegram {
  background-color: #0088cc;
}

.whatsapp {
  background-color: #25d366;
}

.web {
  background-color: #4a6cf7;
}

.botProviderId {
  font-family: monospace;
  font-size: 0.85rem;
  color: #666;
}

/* Messages tab styles */
.messagesSection {
  padding: 1rem 0;
}

.messagesSection h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.messagesTable {
  margin-top: 1.5rem;
  overflow-x: auto;
}

.messagesTable .table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.messagesTable th {
  background-color: #f7f9fc;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #444;
  border-bottom: 2px solid #eaedf3;
  position: sticky;
  top: 0;
  z-index: 10;
}

.messagesTable td {
  padding: 0.75rem;
  border-bottom: 1px solid #eee;
  vertical-align: top;
  transition: background-color 0.2s;
}

.botMessage {
  background-color: #f0f7ff;
}

.botMessage:hover {
  background-color: #e5f0ff;
}

.userMessage {
  background-color: #fff;
}

.userMessage:hover {
  background-color: #f9f9f9;
}

.messageTime {
  white-space: nowrap;
  color: #666;
  font-size: 0.8rem;
}

.messageUser {
  font-weight: 500;
  color: #333;
}

.messageUserId {
  font-size: 0.75rem;
  color: #777;
  margin-top: 0.25rem;
  font-family: monospace;
  display: block;
  opacity: 0.8;
}

.messagePlatform {
  text-align: center;
}

.platformBadge {
  display: inline-block;
  padding: 0.4rem 0.6rem;
  border-radius: 5px;
  font-size: 0.75rem;
  color: white;
  text-transform: capitalize;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.platformBadge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.telegram {
  background-color: #0088cc;
}

.whatsapp {
  background-color: #25D366;
}

.messageDirection {
  white-space: nowrap;
  font-weight: 500;
}

.botMessage .messageDirection {
  color: #0070f3;
  position: relative;
  display: flex;
  align-items: center;
}

.userMessage .messageDirection {
  color: #666;
  position: relative;
  display: flex;
  align-items: center;
}

.botMessage .messageDirection:before {
  content: "↑";
  margin-right: 4px;
  font-size: 0.9rem;
}

.userMessage .messageDirection:before {
  content: "↓";
  margin-right: 4px;
  font-size: 0.9rem;
}

.messageContent {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.25rem 0;
  line-height: 1.4;
}

.messageContent em {
  color: #999;
  font-style: italic;
}

.messageHasImage {
  text-align: center;
}

.hasImageBadge, .noImageBadge {
  display: inline-block;
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 999px;
  transition: transform 0.2s;
}

.hasImageBadge:hover, .noImageBadge:hover {
  transform: translateY(-1px);
}

.hasImageBadge {
  background-color: #4a6cf7;
  color: white;
  box-shadow: 0 1px 3px rgba(74, 108, 247, 0.3);
}

.noImageBadge {
  background-color: #eee;
  color: #666;
}

/* View Image Button */
.viewImageButton {
  background-color: #4a6cf7;
  color: white;
  border: none;
  border-radius: 999px;
  padding: 0.25rem 0.7rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(74, 108, 247, 0.3);
}

.viewImageButton:hover {
  background-color: #3957d8;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(74, 108, 247, 0.4);
}

.viewImageButton:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(74, 108, 247, 0.3);
}

/* Image Modal Styles */
.imageModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.imageModal {
  background-color: white;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90vh;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.closeModalButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.closeModalButton:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.modalImageContainer {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
  width: 100%;
  height: 100%;
}

.modalImage {
  max-width: 100%;
  max-height: calc(90vh - 40px); /* Subtract padding from max height */
  object-fit: contain;
  border-radius: 4px;
  display: block;
}

/* Styles for modal details */
.modalDetails {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: 40vh;
  background-color: white;
}

@media (min-width: 768px) {
  .imageModal {
    display: grid;
    grid-template-columns: 1fr 400px;
    max-width: 1000px;
    max-height: 80vh;
  }
  
  .modalImageContainer {
    height: 80vh;
  }
  
  .modalDetails {
    max-height: 80vh;
    border-left: 1px solid #eee;
  }
}

/* Admin navigation */
.adminNav {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 2rem;
  margin-top: 1.5rem;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}
.adminNav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.navLink {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  color: #777;
  transition: color 0.3s;
  text-decoration: none;
  white-space: nowrap;
}

.navLink:hover {
  color: #333;
}

.activeLink {
  color: #0070f3;
  font-weight: 600;
}

.activeLink::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0070f3;
}

.adminContent {
  min-height: 300px;
}

/* Messages tab styles with real-time features */
.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.sectionHeader h2 {
  margin: 0;
}

@media (max-width: 768px) {
  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .controlsGroup {
    width: 100%;
    align-items: flex-start;
  }
  
  .togglesContainer {
    width: 100%;
    justify-content: space-between;
  }
  
  .realtimeControl, .soundControl {
    margin-bottom: 0.5rem;
  }
  
  .realtimeToggle, .soundToggle {
    min-width: 150px;
  }
  
  .refreshButton {
    align-self: flex-end;
    margin-top: 0.5rem;
  }
  
  /* Table adjustments for mobile */
  .messagesTable {
    overflow-x: auto;
  }
  
  /* Reduce padding in table cells for mobile */
  .messagesTable td, .messagesTable th {
    padding: 0.5rem;
    font-size: 0.9rem;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .togglesContainer {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .realtimeToggle, .soundToggle {
    min-width: 200px;
  }
  
  .refreshButton {
    align-self: center;
    width: 100%;
    margin-top: 1rem;
  }
}

.controlsGroup {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.togglesContainer {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.realtimeControl, .soundControl {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.realtimeToggle, .soundToggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  user-select: none;
  color: #555;
  min-width: 180px;
}

.toggleSwitch {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 24px;
  background-color: #ccc;
  border-radius: 24px;
  margin-right: 8px;
  transition: all 0.3s;
  flex-shrink: 0;
}

.realtimeToggle input, .soundToggle input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.toggleSwitch:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s;
}

.realtimeToggle input:checked + .toggleSwitch, .soundToggle input:checked + .toggleSwitch {
  background-color: #4a6cf7;
}

.realtimeToggle input:checked + .toggleSwitch:before, .soundToggle input:checked + .toggleSwitch:before {
  transform: translateX(22px);
}

.toggleStatus {
  display: inline-block;
  min-width: 30px;
  font-weight: 600;
  margin-left: 4px;
  font-size: 0.8rem;
}

.realtimeToggle input:checked ~ .toggleStatus, .soundToggle input:checked ~ .toggleStatus {
  color: #4a6cf7;
}

.realtimeToggle input:not(:checked) ~ .toggleStatus, .soundToggle input:not(:checked) ~ .toggleStatus {
  color: #888;
}

.refreshButton {
  padding: 0.5rem 1rem;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  color: #555;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.refreshButton:hover {
  background-color: #e0e0e0;
}

.refreshButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.realtimeIndicator {
  display: inline-block;
  margin-left: 0.5rem;
  color: #4a6cf7;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Additional styles for the enhanced generations page */
.imageMeta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
}

.tokenCost {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 999px;
  font-size: 0.8125rem;
  color: #fff;
  background-color: #6b46c1;
  margin-bottom: 0.5rem;
}

.imageResolution, .imageStyle {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.userInfoCard {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #4a6cf7;
}

.imageUserDetails {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.imageUserName {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
}

.walletInfo {
  font-size: 0.85rem;
  color: #555;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #eee;
}

/* Simple images grid - No details view */
.imagesSimpleGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.imageSimpleCard {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background-color: #fff;
  transition: transform 0.2s, box-shadow 0.2s;
  aspect-ratio: 1;
}

.imageSimpleCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  cursor: pointer;
}

.imageSimpleCard .cardImageContainer {
  width: 100%;
  height: 100%;
}

/* Details toggle control - improved design */
.detailsControl {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.detailsToggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  user-select: none;
  color: #333;
  font-weight: 500;
}

.detailsToggle input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.toggleSwitch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
  background-color: #e0e0e0;
  border-radius: 26px;
  margin: 0 0.75rem;
  transition: all 0.3s;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.toggleSwitch:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.detailsToggle input:checked + .toggleSwitch {
  background-color: #4a6cf7;
}

.detailsToggle input:checked + .toggleSwitch:before {
  transform: translateX(26px);
}

.toggleLabel {
  font-weight: 500;
  color: #555;
  min-width: 100px;
}

.toggleStatus {
  display: inline-block;
  font-weight: 600;
  min-width: 30px;
  color: #777;
}

.detailsToggle input:checked ~ .toggleStatus {
  color: #4a6cf7;
}

/* Image Editor Styles */
.pageContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.pageHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.pageHeader h2 {
  color: #333;
  margin-bottom: 0.5rem;
}

.pageHeader p {
  color: #666;
  font-size: 1.1rem;
}

.nsfwNotice {
  color: #ff6b35 !important;
  font-weight: 600;
  font-size: 0.95rem !important;
  background: #fff3e0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border-left: 4px solid #ff6b35;
  margin-top: 1rem;
  display: inline-block;
}

.imageEditorContainer {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.uploadSection {
  display: flex;
  justify-content: center;
}

.dropZone {
  width: 100%;
  max-width: 600px;
  min-height: 300px;
  border: 3px dashed #ccc;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dropZone:hover {
  border-color: #4a6cf7;
  background-color: #f8f9ff;
}

.dropZone.hasImage {
  border: 3px solid #4a6cf7;
  background: transparent;
}

.uploadPrompt {
  text-align: center;
  color: #666;
  padding: 2rem;
}

.uploadPrompt h3 {
  margin: 1rem 0;
  color: #333;
}

.supportedFormats {
  font-size: 0.9rem;
  color: #999;
  margin-top: 0.5rem;
}

.selectedImageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.selectedImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.selectedImageContainer:hover .imageOverlay {
  opacity: 1;
}

.editControls {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.promptSection {
  margin-bottom: 1.5rem;
}

.promptSection label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
}

.promptInput {
  width: 100%;
  padding: 1rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  transition: border-color 0.3s ease;
}

.promptInput:focus {
  border-color: #4a6cf7;
  outline: none;
}

.controlButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.settingsButton {
  padding: 0.75rem 1.5rem;
  background: #f8f9fa;
  border: 2px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.settingsButton:hover {
  border-color: #4a6cf7;
  background: #f0f4ff;
}

.settingsButton.active {
  background: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.editButton {
  padding: 0.75rem 2rem;
  background: #4a6cf7;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.editButton:hover:not(:disabled) {
  background: #3b5bdb;
  transform: translateY(-2px);
}

.editButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.resetButton {
  padding: 0.75rem 1.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.resetButton:hover {
  background: #5a6268;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.settingsPanel {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.settingsPanel h4 {
  margin-bottom: 1.5rem;
  color: #333;
}

.settingsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.settingItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.settingItem label {
  font-weight: 500;
  color: #333;
}

.settingItem input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
}

.settingItem input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #4a6cf7;
  cursor: pointer;
}

.settingItem select,
.settingItem input[type="number"] {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.settingItem small {
  color: #666;
  font-size: 0.8rem;
}

.errorMessage {
  padding: 1rem;
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  margin: 1rem 0;
}

.resultsSection {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.resultsSection h3 {
  margin-bottom: 1.5rem;
  color: #333;
}

.imageComparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.imagePanel {
  text-align: center;
}

.imagePanel h4 {
  margin-bottom: 1rem;
  color: #333;
}

.resultImage {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.downloadButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  margin-right: auto;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.downloadButton:hover {
  background: #218838;
}

.historySection {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.historySection h3 {
  margin-bottom: 1.5rem;
  color: #333;
}

.historyList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.historyItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.historyImages {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
}

.historyThumbnail {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.historyDetails {
  flex: 1;
  position: relative;
}

.historyDetails p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.downloadHistoryButton {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.25rem 0.5rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.downloadHistoryButton:hover {
  background: #5a6268;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pageContainer {
    padding: 1rem;
  }
  
  .controlButtons {
    flex-direction: column;
  }
  
  .settingsGrid {
    grid-template-columns: 1fr;
  }
  
  .imageComparison {
    grid-template-columns: 1fr;
  }
  
  .historyItem {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .downloadHistoryButton {
    position: static;
    margin-top: 0.5rem;
  }
}

/* Video Generations Page Styles */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.statCard h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statNumber {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
}

/* Video Requests Page Styles */
.videoGallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.videoSimpleGallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.videoSimpleCard {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.videoSimpleCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Simple video card styling - controls are handled at component level */
.videoSimpleCard .simple-video-player video {
  transition: all 0.3s ease;
}

.videoCard {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.videoCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.videoPreview {
  position: relative;
  background: #000;
  overflow: hidden;
}

.videoDetails {
  padding: 1rem;
}

.videoHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.videoHeader h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.regeneratedBadge {
  background: #ffc107;
  color: #333;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.videoMeta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.metaRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metaLabel {
  font-weight: 500;
  color: #666;
  font-size: 0.85rem;
}

.metaValue {
  color: #333;
  font-size: 0.85rem;
}

.videoPrompt {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.promptText {
  margin: 0.5rem 0 0 0;
  font-size: 0.85rem;
  color: #555;
  line-height: 1.4;
  max-height: 3.6em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.videoActions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.videoActions .actionButton {
  flex: 1;
  min-width: 0;
  padding: 0.5rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #495057;
  text-decoration: none;
  font-size: 0.8rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.videoActions .actionButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #212529;
}

.tableContainer {
  overflow-x: auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.adminTable {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

.adminTable th,
.adminTable td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.adminTable th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.adminTable tr:hover {
  background: #f8f9fa;
}

.userInfo div:first-child {
  font-weight: 500;
}

.characterInfo div:first-child {
  font-weight: 500;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Responsive status badge for smaller screens */
@media (max-width: 768px) {
  .statusBadge {
    font-size: 0.7rem;
    padding: 3px 6px;
  }
}

@media (max-width: 640px) {
  .statusBadge {
    font-size: 0.65rem;
    padding: 2px 4px;
    gap: 2px;
  }
}

.progressInfo div:first-child {
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.progressBar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  transition: width 0.3s ease;
}

.costInfo div:first-child {
  font-weight: 500;
}

.videoLink {
  color: #007bff;
  text-decoration: none;
  font-size: 0.9rem;
}

.videoLink:hover {
  text-decoration: underline;
}

.actionButtons {
  display: flex;
  gap: 4px;
}

.actionButton {
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.actionButton:hover {
  background: #e9ecef;
}

.emptyState {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.errorMessage {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
}

.retryButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retryButton:hover {
  background: #0056b3;
}

@media (max-width: 768px) {
  .adminNav {
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    gap: 0.5rem;
  }
  .navLink {
    padding: 1rem 1rem;
    font-size: 0.95rem;
  }
}

/* Add missing styles for image-to-video-generations page */
.header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.header p {
  color: #666;
  margin: 0;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statLabel {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.5rem;
}

.filters {
  display: flex;
  gap: 1rem;
  align-items: end;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterGroup label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
}

.filterGroup select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.875rem;
}

.refreshButton {
  padding: 0.5rem 1rem;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  height: fit-content;
}

.refreshButton:hover {
  background-color: #0056b3;
}

.requestsTable {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.requestsTable table {
  width: 100%;
  border-collapse: collapse;
}

.requestsTable th,
.requestsTable td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.requestsTable th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 0.875rem;
}

/* Status column specific styling */
.requestsTable th:nth-child(3),
.requestsTable td:nth-child(3) {
  width: 120px;
  min-width: 100px;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
  .requestsTable {
    overflow-x: auto;
  }

  .requestsTable th,
  .requestsTable td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  /* Make status column more compact on mobile */
  .requestsTable th:nth-child(3),
  .requestsTable td:nth-child(3) {
    width: 90px;
    min-width: 80px;
  }
}

@media (max-width: 640px) {
  .requestsTable th,
  .requestsTable td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  /* Even more compact status column on small screens */
  .requestsTable th:nth-child(3),
  .requestsTable td:nth-child(3) {
    width: 80px;
    min-width: 70px;
  }
}

.requestsTable tr:hover {
  background-color: #f8f9fa;
}

.userInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.userInfo div:first-child {
  font-weight: 500;
}

.userInfo small {
  color: #666;
  font-family: monospace;
}

.userRole {
  font-size: 0.75rem;
  color: #0070f3;
  font-weight: 500;
}

.imagePreview {
  width: 80px;
  height: 80px;
  position: relative;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.videoIcon {
  position: absolute;
  top: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
  color: white;
}

.videoIcon:hover {
  background: rgba(0, 0, 0, 0.9);
}

.videoInfo {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.videoInfo p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.videoInfo strong {
  color: #333;
}

.videoPreview {
  width: 120px;
  height: 80px;
}

.thumbnailVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.noVideo {
  color: #999;
  font-style: italic;
  font-size: 0.875rem;
}

.promptText {
  max-width: 200px;
  word-break: break-word;
  font-size: 0.875rem;
  line-height: 1.4;
}

.settings {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.cost {
  font-weight: 500;
  color: #0070f3;
  font-size: 0.875rem;
}

.date {
  font-size: 0.875rem;
  color: #666;
}

.noResults {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-style: italic;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.error {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border: 1px solid #fecaca;
}

/* Video viewing functionality */
.videoContainer {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.viewButton {
  padding: 0.25rem 0.75rem;
  background-color: #0070f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.viewButton:hover {
  background-color: #0056b3;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modalContent {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modalCloseButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.modalCloseButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

.modalVideoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 800px;
  max-height: 600px;
}

.modalVideo {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

.modalActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.downloadButton,
.openButton {
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  transition: background-color 0.2s;
  text-align: center;
}

.downloadButton {
  background-color: #10b981;
  color: white;
}

.downloadButton:hover {
  background-color: #059669;
  color: white;
}

.openButton {
  background-color: #6b7280;
  color: white;
}

.openButton:hover {
  background-color: #4b5563;
  color: white;
}

@media (max-width: 768px) {
  .modalOverlay {
    padding: 1rem;
  }
  
  .modalContent {
    padding: 1rem;
  }
  
  .modalActions {
    flex-direction: column;
  }
  
  .downloadButton,
  .openButton {
    width: 100%;
  }
}

/* Compact video modal styles for image-to-video generations */
.compactModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.compactModalContent {
  background-color: #000;
  border-radius: 8px;
  padding: 0;
  max-width: min(60vw, 600px);
  max-height: min(60vh, 450px);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compactModalCloseButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(0, 0, 0, 0.8);
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
  z-index: 1001;
  font-weight: bold;
  line-height: 1;
}

.compactModalCloseButton:hover {
  background-color: rgba(0, 0, 0, 1);
}

.compactModalVideo {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .compactModalOverlay {
    padding: 0.5rem;
  }
  
  .compactModalContent {
    max-width: 90vw;
    max-height: 70vh;
  }
  
  .compactModalCloseButton {
    top: 0.5rem;
    right: 0.5rem;
    width: 28px;
    height: 28px;
    font-size: 1rem;
  }
}