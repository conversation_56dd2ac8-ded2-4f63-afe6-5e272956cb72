"use client";

import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';

const CONFIG_KEY = "mk_image_generator";

export default function ImageGeneratorSettingsPage() {
  const { user } = useSelector((state: RootState) => state.user);
  const [options, setOptions] = useState<string[]>([]);
  const [selected, setSelected] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchOptionsAndCurrentSetting();
  }, []);

  const fetchOptionsAndCurrentSetting = async () => {
    setLoading(true);
    setMessage(null);
    const { data, error } = await supabase
      .from("config_options")
      .select("value")
      .eq("key", CONFIG_KEY)
      .single();
    if (error || !data || !data.value || !data.value.options) {
      setMessage("Could not load options from Supabase.");
      setOptions([]);
      setSelected("");
    } else {
      setOptions(data.value.options);
      if (data.value.selected && data.value.options.includes(data.value.selected)) {
        setSelected(data.value.selected);
      } else {
        setSelected(data.value.options[0] || "");
      }
    }
    setLoading(false);
  };

  const handleSave = async () => {
    setSaving(true);
    setMessage(null);
    // Save the selected option in the same row, as a 'selected' property
    const { error } = await supabase
      .from("config_options")
      .upsert([
        { key: CONFIG_KEY, value: { options, selected }, updated_at: new Date().toISOString() },
      ]);
    if (error) {
      setMessage("Failed to save setting.");
    } else {
      setMessage("Setting saved successfully.");
    }
    setSaving(false);
  };

  return (
    <div style={{ maxWidth: 500, margin: "2rem auto", padding: 24, background: "#fff", borderRadius: 8, boxShadow: "0 2px 8px #0001" }}>
      <h2>Image Generator Settings</h2>
      <p>Select which image generator to use for the Mortal Kombat generator.</p>
      <div style={{ margin: "1rem 0" }}>
        <select
          value={selected}
          onChange={e => setSelected(e.target.value)}
          disabled={loading || saving || options.length === 0}
          style={{ padding: 8, fontSize: 16 }}
        >
          {options.map(opt => (
            <option key={opt} value={opt}>{opt}</option>
          ))}
        </select>
      </div>
      <button onClick={handleSave} disabled={saving || loading || !selected} style={{ padding: "8px 16px", fontSize: 16 }}>
        {saving ? "Saving..." : "Save"}
      </button>
      {message && <div style={{ marginTop: 16, color: message.includes("success") ? "green" : "red" }}>{message}</div>}
    </div>
  );
} 