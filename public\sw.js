const CACHE_NAME = 'aivis-images-v1';
const IMAGE_CACHE_URLS = [
  'https://uhlmctpwxyubiyhakxqs.supabase.co',
  'https://v3.fal.media'
];

self.addEventListener('install', (event) => {
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter(cacheName => cacheName !== CACHE_NAME)
          .map(cacheName => caches.delete(cacheName))
      );
    })
  );
});

self.addEventListener('fetch', (event) => {
  // Only cache image requests from our specified domains
  const url = new URL(event.request.url);
  const isImageRequest = IMAGE_CACHE_URLS.some(domain => url.href.includes(domain)) && 
                        (event.request.destination === 'image' || 
                         url.pathname.match(/\.(jpg|jpeg|png|gif|webp|avif)$/i));

  if (isImageRequest) {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response) {
            return response;
          }

          return fetch(event.request).then((fetchResponse) => {
            // Clone the response before caching
            const responseClone = fetchResponse.clone();
            if (fetchResponse.status === 200) {
              cache.put(event.request, responseClone);
            }
            return fetchResponse;
          }).catch(() => {
            // Return a placeholder image if fetch fails
            return new Response(
              '<svg width="200" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="200" height="300" fill="#f0f0f0"/><text x="50%" y="50%" text-anchor="middle" fill="#999" font-size="14">Image unavailable</text></svg>',
              { headers: { 'Content-Type': 'image/svg+xml' } }
            );
          });
        });
      })
    );
  }
}); 