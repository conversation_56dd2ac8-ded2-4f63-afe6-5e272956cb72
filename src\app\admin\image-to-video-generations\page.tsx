'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import styles from '../admin.module.css';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface ImageToVideoRequest {
  id: string;
  user_id: string;
  wallet_id: string;
  status: string;
  image_url: string;
  video_url?: string;
  prompt: string;
  duration: number;
  resolution: string;
  token_cost: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
  user_email?: string;
  user_role?: string;
}

export default function ImageToVideoGenerationsPage() {
  const [requests, setRequests] = useState<ImageToVideoRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'created_at' | 'status'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    fetchRequests();
  }, [filter, sortBy, sortOrder]);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('image_to_video_requests')
        .select('*')
        .order(sortBy, { ascending: sortOrder === 'asc' });

      if (filter !== 'all') {
        query = query.eq('status', filter);
      }

      const { data: requestsData, error: requestsError } = await query;

      if (requestsError) {
        console.error('Error fetching image-to-video requests:', requestsError);
        throw requestsError;
      }

      const requestsWithUserData = await Promise.all(
        (requestsData || []).map(async (request) => {
          if (request.user_id) {
            const { data: profileData } = await supabase
              .from('profiles')
              .select('email, role')
              .eq('user_id', request.user_id)
              .single();
            
            return {
              ...request,
              user_email: profileData?.email || 'No email',
              user_role: profileData?.role || 'user'
            };
          }
          return {
            ...request,
            user_email: 'No email',
            user_role: 'user'
          };
        })
      );

      setRequests(requestsWithUserData);
    } catch (err) {
      console.error('Error fetching requests:', err);
      setError('Failed to load image-to-video requests');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#10b981';
      case 'processing':
        return '#f59e0b';
      case 'failed':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'processing':
        return '⏳';
      case 'failed':
        return '❌';
      default:
        return '⏸️';
    }
  };

  const totalRequests = requests.length;
  const completedRequests = requests.filter(r => r.status === 'completed').length;
  const failedRequests = requests.filter(r => r.status === 'failed').length;
  const processingRequests = requests.filter(r => r.status === 'processing').length;

  if (loading) {
    return (
      <div className={styles.container}>
        <h1>Image to Video Generations</h1>
        <div className={styles.loading}>Loading...</div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Image to Video Generations</h1>
        <p>Manage and monitor image-to-video generation requests</p>
      </div>

      <div className={styles.stats}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{totalRequests}</div>
          <div className={styles.statLabel}>Total Requests</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{completedRequests}</div>
          <div className={styles.statLabel}>Completed</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{processingRequests}</div>
          <div className={styles.statLabel}>Processing</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{failedRequests}</div>
          <div className={styles.statLabel}>Failed</div>
        </div>
      </div>

      <div className={styles.filters}>
        <div className={styles.filterGroup}>
          <label>Filter by Status:</label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label>Sort by:</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'created_at' | 'status')}
          >
            <option value="created_at">Date Created</option>
            <option value="status">Status</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label>Order:</label>
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </select>
        </div>

        <button onClick={fetchRequests} className={styles.refreshButton}>
          Refresh
        </button>
      </div>

      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      <div className={styles.requestsTable}>
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>User</th>
              <th>Status</th>
              <th>Image</th>
              <th>Video</th>
              <th>Prompt</th>
              <th>Settings</th>
              <th>Cost</th>
              <th>Created</th>
              <th>Completed</th>
            </tr>
          </thead>
          <tbody>
            {requests.map((request) => (
              <tr key={request.id}>
                <td>
                  <code>{request.id.slice(0, 8)}</code>
                </td>
                <td>
                  <div className={styles.userInfo}>
                    <div>{request.user_email || 'No email'}</div>
                    <small>{request.user_id.slice(0, 8)}</small>
                    {request.user_role && (
                      <div className={styles.userRole}>{request.user_role}</div>
                    )}
                  </div>
                </td>
                <td>
                  <span
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(request.status) }}
                  >
                    {getStatusIcon(request.status)} {request.status}
                  </span>
                </td>
                <td>
                  <div className={styles.imagePreview}>
                    <img
                      src={request.image_url}
                      alt="Source"
                      className={styles.thumbnailImage}
                    />
                  </div>
                </td>
                <td>
                  {request.video_url ? (
                    <div className={styles.videoPreview}>
                      <video
                        src={request.video_url}
                        className={styles.thumbnailVideo}
                        controls
                        preload="metadata"
                      />
                    </div>
                  ) : (
                    <span className={styles.noVideo}>No video</span>
                  )}
                </td>
                <td>
                  <div className={styles.promptText}>
                    {request.prompt.substring(0, 100)}
                    {request.prompt.length > 100 && '...'}
                  </div>
                </td>
                <td>
                  <div className={styles.settings}>
                    <div>{request.duration}s</div>
                    <div>{request.resolution}</div>
                  </div>
                </td>
                <td>
                  <div className={styles.cost}>
                    {request.token_cost} tokens
                  </div>
                </td>
                <td>
                  <div className={styles.date}>
                    {formatDate(request.created_at)}
                  </div>
                </td>
                <td>
                  <div className={styles.date}>
                    {request.completed_at ? formatDate(request.completed_at) : '-'}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {requests.length === 0 && (
          <div className={styles.noResults}>
            No image-to-video requests found
          </div>
        )}
      </div>
    </div>
  );
}