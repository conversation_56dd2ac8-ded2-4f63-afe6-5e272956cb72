"use client";

import Link from "next/link";
import styles from "../admin.module.css";

export default function AdminGenerationsManagement() {
  return (
    <div>
      <h1>Admin generations management</h1>
      <nav className={styles.adminNav} style={{ marginBottom: 32 }}>
        <Link href="/admin/generations" className={styles.navLink}>
          Telegram generations
        </Link>
        <Link href="/admin/mk-generations" className={styles.navLink}>
          Mortal Kombat generations
        </Link>
        <Link href="/admin/video-generations" className={styles.navLink}>
          Multi Step Requests
        </Link>
        <Link href="/admin/video-requests" className={styles.navLink}>
          Web Video Generations
        </Link>
        <Link href="/admin/image-generations" className={styles.navLink}>
          Web Image Generations
        </Link>
        <Link href="/admin/logo-generations" className={styles.navLink}>
          Logo Generations
        </Link>
        <Link href="/admin/free-generations" className={styles.navLink}>
          Free Generations
        </Link>
      </nav>
      <p>Select a generation type from above to manage its data and settings.</p>
    </div>
  );
} 