"use client";

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { supabase } from '../../../lib/supabaseClient';
import styles from '../admin.module.css';

interface VideoGeneration {
  id: string;
  user_id: string;
  user_email: string;
  user_role: string;
  flow_type: string;
  status: string;
  character: string;
  total_cost: number;
  estimated_cost: number;
  total_tokens: number;
  created_at: string;
  updated_at: string;
  current_step: number;
  total_steps: number;
  latest_completed_step: number;
  has_video: boolean;
  video_url?: string;
  steps_count: number;
  completed_steps_count: number;
}

interface VideoGenerationsData {
  requests: VideoGeneration[];
  total_count: number;
  completed_count: number;
  pending_count: number;
  error_count: number;
}

export default function AdminVideoGenerations() {
  const { user } = useSelector((state: RootState) => state.user);
  const [data, setData] = useState<VideoGenerationsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchVideoGenerations();
  }, []);

  const fetchVideoGenerations = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/admin/video-generations', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err: any) {
      console.error('Error fetching video generations:', err);
      setError(err.message || 'Failed to fetch video generations');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'error': return '#dc3545';
      case 'pending': return '#ffc107';
      default: return '#6c757d';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (user?.role !== 'admin') {
    return (
      <div className={styles.errorMessage}>
        <h2>Access Denied</h2>
        <p>Admin access required to view this page.</p>
      </div>
    );
  }

  if (loading) {
    return <div className={styles.loading}>Loading video generations...</div>;
  }

  if (error) {
    return (
      <div className={styles.errorMessage}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={fetchVideoGenerations} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return <div className={styles.errorMessage}>No data available</div>;
  }

  return (
    <div className={styles.adminPage}>
      <h2>Multi Step Requests Management</h2>
      
      {/* Statistics Summary */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3>Total Requests</h3>
          <div className={styles.statNumber}>{data.total_count}</div>
        </div>
        <div className={styles.statCard}>
          <h3>Completed</h3>
          <div className={styles.statNumber} style={{ color: '#28a745' }}>
            {data.completed_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Pending</h3>
          <div className={styles.statNumber} style={{ color: '#ffc107' }}>
            {data.pending_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Errors</h3>
          <div className={styles.statNumber} style={{ color: '#dc3545' }}>
            {data.error_count}
          </div>
        </div>
      </div>

      {/* Generations Table */}
      <div className={styles.tableContainer}>
        <table className={styles.adminTable}>
          <thead>
            <tr>
              <th>User</th>
              <th>Character</th>
              <th>Status</th>
              <th>Progress</th>
              <th>Cost</th>
              <th>Tokens</th>
              <th>Created</th>
              <th>Video</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.requests.map((generation) => (
              <tr key={generation.id}>
                <td>
                  <div className={styles.userInfo}>
                    <div>{generation.user_email}</div>
                    <small style={{ color: '#666' }}>{generation.user_role}</small>
                  </div>
                </td>
                <td>
                  <div className={styles.characterInfo}>
                    <div style={{ textTransform: 'capitalize' }}>
                      {generation.character || 'N/A'}
                    </div>
                    <small style={{ color: '#666' }}>{generation.flow_type}</small>
                  </div>
                </td>
                <td>
                  <span 
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(generation.status) }}
                  >
                    {generation.status}
                  </span>
                </td>
                <td>
                  <div className={styles.progressInfo}>
                    <div>Step {generation.current_step}/{generation.total_steps}</div>
                    <div className={styles.progressBar}>
                      <div 
                        className={styles.progressFill}
                        style={{ 
                          width: `${(generation.current_step / generation.total_steps) * 100}%`,
                          backgroundColor: getStatusColor(generation.status)
                        }}
                      />
                    </div>
                  </div>
                </td>
                <td>
                  <div className={styles.costInfo}>
                    <div>${generation.total_cost.toFixed(2)}</div>
                    {generation.estimated_cost > 0 && (
                      <small style={{ color: '#666' }}>
                        Est: ${generation.estimated_cost.toFixed(2)}
                      </small>
                    )}
                  </div>
                </td>
                <td>{generation.total_tokens}</td>
                <td>
                  <small>{formatDate(generation.created_at)}</small>
                </td>
                <td>
                  {generation.has_video ? (
                    <a 
                      href={generation.video_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className={styles.videoLink}
                    >
                      ▶️ View
                    </a>
                  ) : (
                    <span style={{ color: '#999' }}>No video</span>
                  )}
                </td>
                <td>
                  <div className={styles.actionButtons}>
                    <button 
                      onClick={() => window.open(`/mortal-kombat-video?id=${generation.id}`, '_blank')}
                      className={styles.actionButton}
                      title="View Details"
                    >
                      👁️
                    </button>
                    <button 
                      onClick={() => navigator.clipboard.writeText(generation.id)}
                      className={styles.actionButton}
                      title="Copy ID"
                    >
                      📋
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {data.requests.length === 0 && (
        <div className={styles.emptyState}>
          <p>No multi-step requests found.</p>
        </div>
      )}
    </div>
  );
} 